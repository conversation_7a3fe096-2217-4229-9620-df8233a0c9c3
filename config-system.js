// ===== SISTEMA DE CONFIGURAÇÕES DINÂMICAS - ESTÚDIO730 =====

/**
 * Gerenciador de Configurações
 * Responsável por gerenciar todas as configurações da página
 */
class ConfigManager {
    constructor() {
        this.storageKey = 'estudio730_config';
        this.defaultConfig = this.getDefaultConfig();
        this.currentConfig = this.loadConfig();
        this.init();
    }

    /**
     * Configuração padrão do sistema
     */
    getDefaultConfig() {
        return {
            links: [
                {
                    id: 'whatsapp',
                    name: 'WhatsApp',
                    url: 'https://wa.me/5511999999999?text=Olá! Gostaria de agendar um horário no Estúdio730.',
                    icon: 'fab fa-whatsapp',
                    color: '#25d366',
                    visible: true,
                    removable: false,
                    order: 1
                },
                {
                    id: 'instagram',
                    name: 'Instagram',
                    url: 'https://www.instagram.com/estudio730/',
                    icon: 'fab fa-instagram',
                    color: '#e4405f',
                    visible: true,
                    removable: false,
                    order: 2
                },
                {
                    id: 'location',
                    name: 'Localização',
                    url: 'https://www.google.com/maps/search/?api=1&query=Rua das Flores, 123, São Paulo, SP',
                    icon: 'fas fa-map-marker-alt',
                    color: '#4285f4',
                    visible: true,
                    removable: false,
                    order: 3
                },
                {
                    id: 'website',
                    name: 'Site Oficial',
                    url: 'https://www.estudio730.com.br',
                    icon: 'fas fa-globe',
                    color: '#6c5ce7',
                    visible: true,
                    removable: false,
                    order: 4
                }
            ],
            settings: {
                lastModified: Date.now(),
                version: '1.0.0'
            }
        };
    }

    /**
     * Inicializa o sistema de configurações
     */
    init() {
        this.bindEvents();
        this.renderLinks();
        this.initMobileOptimizations();
        console.log('🔧 Sistema de Configurações do Estúdio730 inicializado');
    }

    /**
     * Vincula eventos aos elementos da interface
     */
    bindEvents() {
        // Botão de abrir configurações
        const configButton = document.getElementById('config-button');
        configButton?.addEventListener('click', () => this.openModal());

        // Botão de fechar modal
        const closeButton = document.getElementById('config-close');
        closeButton?.addEventListener('click', () => this.closeModal());

        // Fechar modal clicando fora
        const modal = document.getElementById('config-modal');
        modal?.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Tecla ESC para fechar modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal?.classList.contains('active')) {
                this.closeModal();
            }
        });

        // Botão expandir formulário
        const expandButton = document.getElementById('btn-expand-form');
        expandButton?.addEventListener('click', () => this.toggleAddForm());

        // Botão cancelar formulário
        const cancelFormButton = document.getElementById('btn-cancel-form');
        cancelFormButton?.addEventListener('click', () => this.hideAddForm());

        // Formulário de adicionar link
        const addForm = document.getElementById('add-link-form');
        addForm?.addEventListener('submit', (e) => this.handleAddLink(e));

        // Modal de edição
        const editCloseButton = document.getElementById('edit-close');
        editCloseButton?.addEventListener('click', () => this.closeEditModal());

        const cancelEditButton = document.getElementById('btn-cancel-edit');
        cancelEditButton?.addEventListener('click', () => this.closeEditModal());

        const editForm = document.getElementById('edit-link-form');
        editForm?.addEventListener('submit', (e) => this.handleEditLink(e));

        // Fechar modal de edição clicando fora
        const editModal = document.getElementById('edit-modal');
        editModal?.addEventListener('click', (e) => {
            if (e.target === editModal) this.closeEditModal();
        });

        // Tecla ESC para fechar modal de edição
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && editModal?.classList.contains('active')) {
                this.closeEditModal();
            }
        });

        // Seletores de ícones
        const iconSelect = document.getElementById('link-icon');
        iconSelect?.addEventListener('change', () => this.updateIconPreview('icon-preview', iconSelect));

        const editIconSelect = document.getElementById('edit-link-icon');
        editIconSelect?.addEventListener('change', () => this.updateIconPreview('edit-icon-preview', editIconSelect));

        // Botões do footer
        document.getElementById('btn-save')?.addEventListener('click', () => this.saveConfig());
        document.getElementById('btn-cancel')?.addEventListener('click', () => this.closeModal());
        document.getElementById('btn-restore')?.addEventListener('click', () => this.restoreDefaults());
    }

    /**
     * Abre o modal de configurações
     */
    openModal() {
        const modal = document.getElementById('config-modal');
        modal?.classList.add('active');
        this.renderConfigLinks();
        document.body.style.overflow = 'hidden';

        // Inicializar efeitos Magic UI do modal
        setTimeout(() => {
            this.initModalMagicEffects();
        }, 100);
    }

    /**
     * Fecha o modal de configurações
     */
    closeModal() {
        const modal = document.getElementById('config-modal');
        const modalContent = document.querySelector('.config-modal-content');

        if (this.isMobile()) {
            // Animação de saída para mobile
            modal?.classList.add('closing');
            modalContent?.classList.add('closing');

            setTimeout(() => {
                modal?.classList.remove('active', 'closing');
                modalContent?.classList.remove('closing');
                document.body.style.overflow = '';
                this.hideAddForm();
                this.clearForm();
            }, 300);
        } else {
            // Fechamento normal para desktop
            modal?.classList.remove('active');
            document.body.style.overflow = '';
            this.hideAddForm();
            this.clearForm();
        }
    }

    /**
     * Alterna exibição do formulário de adicionar link
     */
    toggleAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        if (container?.classList.contains('expanded')) {
            this.hideAddForm();
        } else {
            this.showAddForm();
        }
    }

    /**
     * Mostra o formulário de adicionar link
     */
    showAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        container?.classList.add('expanded');
        button?.classList.add('hidden');

        // Foca no primeiro campo após a animação
        setTimeout(() => {
            document.getElementById('link-name')?.focus();
        }, 300);
    }

    /**
     * Esconde o formulário de adicionar link
     */
    hideAddForm() {
        const container = document.getElementById('add-link-form-container');
        const button = document.getElementById('btn-expand-form');

        container?.classList.remove('expanded');
        button?.classList.remove('hidden');
        this.clearForm();
    }

    /**
     * Atualiza preview do ícone selecionado
     */
    updateIconPreview(previewId, selectElement) {
        const preview = document.getElementById(previewId);
        const colorInput = previewId === 'icon-preview' ?
            document.getElementById('link-color') :
            document.getElementById('edit-link-color');

        if (!preview || !selectElement) return;

        const selectedOption = selectElement.selectedOptions[0];
        const iconClass = selectElement.value;
        const suggestedColor = selectedOption?.dataset.color;

        if (iconClass) {
            // Atualiza o ícone no preview
            preview.innerHTML = `<i class="${iconClass}"></i>`;
            preview.classList.add('active');

            // Sugere cor padrão se disponível
            if (suggestedColor && colorInput) {
                colorInput.value = suggestedColor;
            }
        } else {
            // Reseta preview se nenhum ícone selecionado
            preview.innerHTML = '<i class="fas fa-question"></i>';
            preview.classList.remove('active');
        }
    }

    /**
     * Carrega configurações do localStorage
     */
    loadConfig() {
        try {
            const saved = localStorage.getItem(this.storageKey);
            if (saved) {
                const config = JSON.parse(saved);
                // Mescla com configuração padrão para garantir compatibilidade
                return this.mergeConfigs(this.defaultConfig, config);
            }
        } catch (error) {
            console.warn('Erro ao carregar configurações:', error);
        }
        return { ...this.defaultConfig };
    }

    /**
     * Salva configurações no localStorage
     */
    saveConfig() {
        try {
            this.currentConfig.settings.lastModified = Date.now();
            localStorage.setItem(this.storageKey, JSON.stringify(this.currentConfig));
            this.renderLinks();
            this.showToast('✅ Configurações salvas com sucesso!', 'success');
            this.closeModal();
            console.log('💾 Configurações salvas:', this.currentConfig);
        } catch (error) {
            console.error('Erro ao salvar configurações:', error);
            this.showToast('❌ Erro ao salvar configurações', 'error');
        }
    }

    /**
     * Restaura configurações padrão
     */
    restoreDefaults() {
        if (confirm('Tem certeza que deseja restaurar as configurações padrão? Todos os links personalizados serão perdidos.')) {
            this.currentConfig = { ...this.defaultConfig };
            this.saveConfig();
            this.renderConfigLinks();
            this.showToast('🔄 Configurações restauradas para o padrão', 'info');
        }
    }

    /**
     * Abre modal de edição de link
     */
    openEditModal(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (!link) return;

        // Preenche os campos do formulário
        document.getElementById('edit-link-id').value = link.id;
        document.getElementById('edit-link-name').value = link.name;
        document.getElementById('edit-link-url').value = link.url;
        document.getElementById('edit-link-icon').value = link.icon;
        document.getElementById('edit-link-color').value = link.color;

        // Atualiza preview do ícone
        const editIconSelect = document.getElementById('edit-link-icon');
        this.updateIconPreview('edit-icon-preview', editIconSelect);

        // Mostra o modal
        const modal = document.getElementById('edit-modal');
        modal?.classList.add('active');
        document.body.style.overflow = 'hidden';

        // Inicializar efeitos Magic UI do modal de edição
        setTimeout(() => {
            this.initEditModalMagicEffects();
            document.getElementById('edit-link-name')?.focus();
        }, 300);
    }

    /**
     * Inicializa efeitos Magic UI no modal de edição
     */
    initEditModalMagicEffects() {
        const editModal = document.querySelector('.edit-modal-content');
        if (!editModal) return;

        // Efeito spotlight
        editModal.addEventListener('mousemove', (e) => {
            const rect = editModal.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            editModal.style.setProperty('--mouse-x', `${x}%`);
            editModal.style.setProperty('--mouse-y', `${y}%`);
        });

        // Efeitos ripple para botões
        const buttons = editModal.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }

    /**
     * Fecha modal de edição
     */
    closeEditModal() {
        const modal = document.getElementById('edit-modal');
        modal?.classList.remove('active');
        document.body.style.overflow = '';
        this.clearEditForm();
    }

    /**
     * Limpa formulário de edição
     */
    clearEditForm() {
        const form = document.getElementById('edit-link-form');
        if (form) {
            form.reset();

            // Reseta preview do ícone
            const preview = document.getElementById('edit-icon-preview');
            if (preview) {
                preview.innerHTML = '<i class="fas fa-question"></i>';
                preview.classList.remove('active');
            }
        }
    }

    /**
     * Manipula edição de link
     */
    handleEditLink(e) {
        e.preventDefault();

        const linkId = document.getElementById('edit-link-id').value;
        const name = document.getElementById('edit-link-name').value.trim();
        const url = document.getElementById('edit-link-url').value.trim();
        const icon = document.getElementById('edit-link-icon').value || 'fas fa-link';
        const color = document.getElementById('edit-link-color').value;

        // Validação
        if (!name || !url || !icon) {
            this.showToast('❌ Nome, URL e Ícone são obrigatórios', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('❌ URL inválida', 'error');
            return;
        }

        // Encontra e atualiza o link
        const linkIndex = this.currentConfig.links.findIndex(l => l.id === linkId);
        if (linkIndex === -1) {
            this.showToast('❌ Link não encontrado', 'error');
            return;
        }

        // Atualiza o link
        this.currentConfig.links[linkIndex] = {
            ...this.currentConfig.links[linkIndex],
            name,
            url,
            icon,
            color
        };

        this.renderConfigLinks();
        this.closeEditModal();
        this.showToast(`✅ Link "${name}" atualizado com sucesso!`, 'success');
    }

    /**
     * Mescla configurações
     */
    mergeConfigs(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        if (userConfig.links) {
            // Mantém links padrão e adiciona personalizados
            const defaultLinks = defaultConfig.links;
            const userLinks = userConfig.links;
            
            merged.links = defaultLinks.map(defaultLink => {
                const userLink = userLinks.find(ul => ul.id === defaultLink.id);
                return userLink ? { ...defaultLink, ...userLink } : defaultLink;
            });

            // Adiciona links personalizados
            const customLinks = userLinks.filter(ul => !defaultLinks.some(dl => dl.id === ul.id));
            merged.links.push(...customLinks);
        }

        if (userConfig.settings) {
            merged.settings = { ...merged.settings, ...userConfig.settings };
        }

        return merged;
    }

    /**
     * Renderiza os links na página principal
     */
    renderLinks() {
        const linksSection = document.querySelector('.links-section');
        if (!linksSection) return;

        // Limpa links existentes (exceto os elementos que não são botões)
        const existingButtons = linksSection.querySelectorAll('.link-button');
        existingButtons.forEach(btn => btn.remove());

        // Ordena links por ordem e visibilidade
        const visibleLinks = this.currentConfig.links
            .filter(link => link.visible)
            .sort((a, b) => a.order - b.order);

        // Renderiza cada link
        visibleLinks.forEach(link => {
            const linkElement = this.createLinkElement(link);
            linksSection.appendChild(linkElement);
        });
    }

    /**
     * Cria elemento de link para a página principal
     */
    createLinkElement(link) {
        const linkEl = document.createElement('a');
        linkEl.href = link.url;
        linkEl.className = `link-button custom-link`;
        linkEl.target = '_blank';
        linkEl.rel = 'noopener noreferrer';
        linkEl.style.setProperty('--link-color', link.color);

        linkEl.innerHTML = `
            <div class="button-content">
                <i class="${link.icon}"></i>
                <span class="button-text">
                    <strong>${link.name}</strong>
                    <small>Clique para acessar</small>
                </span>
            </div>
            <i class="fas fa-chevron-right arrow"></i>
        `;

        // Adiciona estilos dinâmicos
        linkEl.addEventListener('mouseenter', () => {
            linkEl.style.borderColor = link.color;
            linkEl.style.boxShadow = `0 15px 40px ${link.color}30`;
        });

        linkEl.addEventListener('mouseleave', () => {
            linkEl.style.borderColor = 'transparent';
            linkEl.style.boxShadow = 'var(--shadow)';
        });

        return linkEl;
    }

    /**
     * Renderiza links no modal de configurações
     */
    renderConfigLinks() {
        const linksList = document.getElementById('links-list');
        if (!linksList) return;

        linksList.innerHTML = '';

        // Ordena links por ordem
        const sortedLinks = [...this.currentConfig.links].sort((a, b) => a.order - b.order);

        sortedLinks.forEach(link => {
            const linkItem = this.createConfigLinkItem(link);
            linksList.appendChild(linkItem);
        });

        // Aplica efeitos Magic UI aos novos elementos
        setTimeout(() => {
            this.initMagicCardEffects();
            this.initReorderAnimations();
        }, 100);
    }

    /**
     * Cria item de link para o modal de configurações
     */
    createConfigLinkItem(link) {
        const item = document.createElement('div');
        item.className = 'link-item';
        item.dataset.linkId = link.id;

        item.innerHTML = `
            <div class="link-item-header">
                <div class="link-info">
                    <div class="link-preview" style="background-color: ${link.color}">
                        <i class="${link.icon}"></i>
                    </div>
                    <div class="link-details">
                        <h4>${link.name}</h4>
                        <small>${this.truncateUrl(link.url)}</small>
                    </div>
                </div>
                <div class="link-controls">
                    <div class="toggle-switch ${link.visible ? 'active' : ''}"
                         onclick="configManager.toggleLinkVisibility('${link.id}')">
                    </div>
                    <button class="control-btn edit" onclick="configManager.openEditModal('${link.id}')"
                            title="Editar link">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="control-btn reorder-btn ${isFirst ? 'disabled' : ''}"
                            data-link-id="${link.id}"
                            data-direction="up"
                            title="${isFirst ? 'Já está no topo' : 'Mover para cima'}"
                            ${isFirst ? 'disabled' : ''}>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <button class="control-btn reorder-btn ${isLast ? 'disabled' : ''}"
                            data-link-id="${link.id}"
                            data-direction="down"
                            title="${isLast ? 'Já está no final' : 'Mover para baixo'}"
                            ${isLast ? 'disabled' : ''}>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    ${link.removable ? `
                        <button class="control-btn danger" onclick="configManager.removeLink('${link.id}')"
                                title="Remover link">
                            <i class="fas fa-trash"></i>
                        </button>
                    ` : ''}
                </div>
            </div>
        `;

        return item;
    }

    /**
     * Trunca URL para exibição
     */
    truncateUrl(url) {
        return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }

    /**
     * Alterna visibilidade do link
     */
    toggleLinkVisibility(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        if (link) {
            link.visible = !link.visible;
            this.renderConfigLinks();
        }
    }

    /**
     * Move link para cima ou para baixo com animações
     */
    moveLink(linkId, direction) {
        const links = this.currentConfig.links;

        // Ordena os links por order para trabalhar com a ordem correta
        const sortedLinks = [...links].sort((a, b) => a.order - b.order);
        const currentIndex = sortedLinks.findIndex(l => l.id === linkId);

        if (currentIndex === -1) return;

        // Calcula o novo índice
        const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

        // Validação de limites com animação de shake
        if (newIndex < 0 || newIndex >= sortedLinks.length) {
            const message = direction === 'up'
                ? '❌ Este item já está no topo da lista'
                : '❌ Este item já está no final da lista';
            this.showToast(message, 'warning');
            this.animateInvalidAction(linkId);
            return;
        }

        // Anima o movimento do link
        this.animateLinkMovement(linkId, direction, () => {
            // Remove o item da posição atual
            const [movedLink] = sortedLinks.splice(currentIndex, 1);

            // Insere na nova posição
            sortedLinks.splice(newIndex, 0, movedLink);

            // Recalcula todas as ordens sequencialmente
            sortedLinks.forEach((link, index) => {
                link.order = index + 1;
            });

            // Atualiza o array original com as novas ordens
            this.currentConfig.links = sortedLinks;

            // Atualiza ambas as renderizações e salva automaticamente
            this.renderConfigLinks();
            this.renderLinks();
            this.saveConfigSilently();

            // Feedback visual de sucesso
            const direction_text = direction === 'up' ? 'para cima' : 'para baixo';
            this.showToast(`✅ Link movido ${direction_text}`, 'success');
        });
    }

    /**
     * Remove link personalizado
     */
    removeLink(linkId) {
        const link = this.currentConfig.links.find(l => l.id === linkId);
        
        if (!link || !link.removable) {
            this.showToast('❌ Este link não pode ser removido', 'error');
            return;
        }

        if (confirm(`Tem certeza que deseja remover o link "${link.name}"?`)) {
            this.currentConfig.links = this.currentConfig.links.filter(l => l.id !== linkId);
            this.renderConfigLinks();
            this.showToast(`🗑️ Link "${link.name}" removido`, 'info');
        }
    }

    /**
     * Manipula adição de novo link
     */
    handleAddLink(e) {
        e.preventDefault();
        
        const name = document.getElementById('link-name').value.trim();
        const url = document.getElementById('link-url').value.trim();
        const icon = document.getElementById('link-icon').value || 'fas fa-link';
        const color = document.getElementById('link-color').value;

        // Validação
        if (!name || !url || !icon) {
            this.showToast('❌ Nome, URL e Ícone são obrigatórios', 'error');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showToast('❌ URL inválida', 'error');
            return;
        }

        // Cria novo link
        const newLink = {
            id: this.generateId(),
            name,
            url,
            icon,
            color,
            visible: true,
            removable: true,
            order: this.currentConfig.links.length + 1
        };

        this.currentConfig.links.push(newLink);
        this.renderConfigLinks();
        this.hideAddForm();
        this.showToast(`✅ Link "${name}" adicionado com sucesso!`, 'success');
    }

    /**
     * Valida URL
     */
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    /**
     * Gera ID único
     */
    generateId() {
        return 'link_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Limpa formulário
     */
    clearForm() {
        const form = document.getElementById('add-link-form');
        if (form) {
            form.reset();
            document.getElementById('link-color').value = '#6c5ce7';

            // Reseta preview do ícone
            const preview = document.getElementById('icon-preview');
            if (preview) {
                preview.innerHTML = '<i class="fas fa-question"></i>';
                preview.classList.remove('active');
            }
        }
    }

    /**
     * Mostra notificação toast
     */
    showToast(message, type = 'info') {
        // Remove toast anterior se existir
        const existingToast = document.querySelector('.config-toast');
        if (existingToast) {
            existingToast.remove();
        }

        // Cria novo toast
        const toast = document.createElement('div');
        toast.className = `config-toast ${type}`;
        toast.textContent = message;

        // Estilos do toast
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };

        toast.style.cssText = `
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: ${colors[type] || colors.info};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 3000;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.3s ease-out;
            max-width: 300px;
            font-size: 0.9rem;
        `;

        document.body.appendChild(toast);

        // Remove toast após 4 segundos
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    /**
     * Inicializa efeitos Magic UI no modal
     */
    initModalMagicEffects() {
        this.initSpotlightEffect();
        this.initRippleEffects();
        this.initMagicCardEffects();
    }

    /**
     * Efeito spotlight que segue o mouse no modal
     */
    initSpotlightEffect() {
        const modalContent = document.querySelector('.config-modal-content');
        if (!modalContent) return;

        modalContent.addEventListener('mousemove', (e) => {
            const rect = modalContent.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width) * 100;
            const y = ((e.clientY - rect.top) / rect.height) * 100;

            modalContent.style.setProperty('--mouse-x', `${x}%`);
            modalContent.style.setProperty('--mouse-y', `${y}%`);
        });
    }

    /**
     * Efeitos ripple para botões do modal
     */
    initRippleEffects() {
        const buttons = document.querySelectorAll('.config-modal button, .edit-modal button');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }

    /**
     * Efeitos Magic Card para os itens de links
     */
    initMagicCardEffects() {
        const linkItems = document.querySelectorAll('.link-item');

        linkItems.forEach(item => {
            item.addEventListener('mousemove', (e) => {
                const rect = item.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;

                item.style.setProperty('--mouse-x', `${x}%`);
                item.style.setProperty('--mouse-y', `${y}%`);
            });
        });
    }

    /**
     * Cria efeito ripple
     */
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(212, 175, 55, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 10;
        `;

        // Garante que o elemento pai tenha position relative
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    /**
     * Inicializa otimizações para mobile
     */
    initMobileOptimizations() {
        this.setupTabNavigation();
        this.setupSwipeGestures();
        this.setupRealTimeValidation();
        this.optimizePerformanceForMobile();
        this.setupTouchOptimizations();
    }

    /**
     * Configura navegação por abas
     */
    setupTabNavigation() {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');
        let isTransitioning = false;

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Prevenir múltiplas transições simultâneas
                if (isTransitioning) return;

                const targetTab = button.dataset.tab;
                const targetPanel = document.getElementById(`${targetTab}-panel`);

                // Se já está ativo, não fazer nada
                if (button.classList.contains('active')) return;

                isTransitioning = true;

                // Usar requestAnimationFrame para otimizar mudanças de DOM
                requestAnimationFrame(() => {
                    // Remove active de todos os botões e painéis
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanels.forEach(panel => panel.classList.remove('active'));

                    // Adiciona active ao botão clicado
                    button.classList.add('active');

                    // Mostra o painel correspondente
                    if (targetPanel) {
                        targetPanel.classList.add('active');
                        // Reset scroll position imediatamente para evitar jump
                        targetPanel.scrollTop = 0;
                    }

                    // Feedback haptic em dispositivos suportados
                    if (navigator.vibrate) {
                        navigator.vibrate(10);
                    }

                    // Liberar flag após animação
                    setTimeout(() => {
                        isTransitioning = false;
                    }, 300);
                });
            });
        });
    }

    /**
     * Configura gestos de swipe para fechar modal
     */
    setupSwipeGestures() {
        if (!this.isMobile()) return;

        const modal = document.querySelector('.config-modal-content');
        const swipeIndicator = document.querySelector('.swipe-handle');
        let startY = 0;
        let currentY = 0;
        let isDragging = false;

        const handleTouchStart = (e) => {
            startY = e.touches[0].clientY;
            isDragging = true;
            modal.style.transition = 'none';
        };

        const handleTouchMove = (e) => {
            if (!isDragging) return;

            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;

            // Só permite swipe para baixo
            if (deltaY > 0) {
                const progress = Math.min(deltaY / 200, 1);
                modal.style.transform = `translateY(${deltaY}px)`;
                modal.style.opacity = 1 - (progress * 0.3);

                // Feedback visual no indicador
                if (swipeIndicator) {
                    swipeIndicator.style.width = `${40 + (progress * 20)}px`;
                    swipeIndicator.style.background = progress > 0.5 ?
                        'var(--accent-color)' : 'rgba(255, 255, 255, 0.3)';
                }
            }
        };

        const handleTouchEnd = () => {
            if (!isDragging) return;
            isDragging = false;

            const deltaY = currentY - startY;
            modal.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

            if (deltaY > 100) {
                // Fechar modal
                this.closeModal();
            } else {
                // Voltar à posição original
                modal.style.transform = 'translateY(0)';
                modal.style.opacity = '1';
            }

            // Reset do indicador
            if (swipeIndicator) {
                swipeIndicator.style.width = '40px';
                swipeIndicator.style.background = 'rgba(255, 255, 255, 0.3)';
            }
        };

        modal?.addEventListener('touchstart', handleTouchStart, { passive: true });
        modal?.addEventListener('touchmove', handleTouchMove, { passive: true });
        modal?.addEventListener('touchend', handleTouchEnd, { passive: true });
    }

    /**
     * Configura validação em tempo real
     */
    setupRealTimeValidation() {
        const urlInputs = document.querySelectorAll('input[type="url"]');

        urlInputs.forEach(input => {
            let validationTimeout;

            input.addEventListener('input', (e) => {
                clearTimeout(validationTimeout);

                // Debounce para evitar muitas validações
                validationTimeout = setTimeout(() => {
                    this.validateUrlInput(input);
                }, 500);
            });
        });
    }

    /**
     * Valida input de URL em tempo real
     */
    validateUrlInput(input) {
        const value = input.value.trim();
        const isValid = value === '' || this.isValidUrl(value);

        // Remove classes anteriores
        input.classList.remove('valid', 'invalid');

        if (value !== '') {
            input.classList.add(isValid ? 'valid' : 'invalid');

            // Feedback visual
            if (isValid) {
                input.style.borderColor = '#27ae60';
                input.style.boxShadow = '0 0 0 2px rgba(39, 174, 96, 0.2)';
            } else {
                input.style.borderColor = '#e74c3c';
                input.style.boxShadow = '0 0 0 2px rgba(231, 76, 60, 0.2)';
            }
        } else {
            input.style.borderColor = '';
            input.style.boxShadow = '';
        }
    }

    /**
     * Otimiza performance para dispositivos móveis
     */
    optimizePerformanceForMobile() {
        if (!this.isMobile()) return;

        // Reduz efeitos Magic UI em mobile
        const modal = document.querySelector('.config-modal-content');
        if (modal) {
            modal.style.setProperty('--reduce-effects', '1');
        }

        // Desabilita animações pesadas em dispositivos lentos
        if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
            document.body.classList.add('reduce-animations');
        }
    }

    /**
     * Configura otimizações de touch
     */
    setupTouchOptimizations() {
        if (!this.isMobile()) return;

        // Adiciona classe para otimizações touch
        document.body.classList.add('touch-device');

        // Melhora feedback de toque
        const touchElements = document.querySelectorAll('button, .link-item, .toggle-switch');

        touchElements.forEach(element => {
            element.addEventListener('touchstart', () => {
                element.style.transform = 'scale(0.98)';
            }, { passive: true });

            element.addEventListener('touchend', () => {
                setTimeout(() => {
                    element.style.transform = '';
                }, 100);
            }, { passive: true });
        });
    }

    /**
     * Detecta se é dispositivo móvel
     */
    isMobile() {
        return window.innerWidth <= 767 ||
               /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    /**
     * ===== FUNÇÕES DE ANIMAÇÃO PARA REORDENAÇÃO =====
     */

    /**
     * Anima ação inválida (shake effect)
     */
    animateInvalidAction(linkId) {
        const linkElement = document.querySelector(`[data-link-id="${linkId}"]`);
        if (linkElement) {
            linkElement.classList.add('shake');
            setTimeout(() => {
                linkElement.classList.remove('shake');
            }, 500);
        }

        // Anima também o botão que foi clicado
        const buttons = document.querySelectorAll(`[onclick*="${linkId}"]`);
        buttons.forEach(button => {
            if (button.classList.contains('control-btn')) {
                button.classList.add('shake');
                setTimeout(() => {
                    button.classList.remove('shake');
                }, 500);
            }
        });
    }

    /**
     * Anima movimento do link
     */
    animateLinkMovement(linkId, direction, callback) {
        const linkElement = document.querySelector(`[data-link-id="${linkId}"]`);
        if (!linkElement) {
            callback();
            return;
        }

        // Adiciona classe de movimento
        linkElement.classList.add('moving');

        // Cria placeholder
        const placeholder = document.createElement('div');
        placeholder.className = 'link-item-placeholder';

        // Insere placeholder na posição correta
        if (direction === 'up') {
            linkElement.parentNode.insertBefore(placeholder, linkElement);
            linkElement.classList.add('moving-up');
        } else {
            linkElement.parentNode.insertBefore(placeholder, linkElement.nextSibling);
            linkElement.classList.add('moving-down');
        }

        // Aguarda animação e executa callback
        setTimeout(() => {
            callback();

            // Remove placeholder após renderização
            setTimeout(() => {
                const remainingPlaceholder = document.querySelector('.link-item-placeholder');
                if (remainingPlaceholder) {
                    remainingPlaceholder.remove();
                }
            }, 100);
        }, 300);
    }

    /**
     * Adiciona efeito ripple aos botões de controle
     */
    addRippleEffect(button, event) {
        const ripple = document.createElement('span');
        ripple.className = 'ripple';

        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';

        button.appendChild(ripple);

        // Remove ripple após animação
        setTimeout(() => {
            ripple.remove();
        }, 300);
    }

    /**
     * Anima clique do botão
     */
    animateButtonClick(button) {
        button.classList.add('clicking');
        setTimeout(() => {
            button.classList.remove('clicking');
        }, 200);
    }

    /**
     * Adiciona feedback de sucesso ao botão
     */
    animateButtonSuccess(button) {
        button.classList.add('success');
        setTimeout(() => {
            button.classList.remove('success');
        }, 600);
    }

    /**
     * Inicializa animações para botões de reordenação
     */
    initReorderAnimations() {
        const reorderButtons = document.querySelectorAll('.reorder-btn');

        reorderButtons.forEach(button => {
            // Remove event listeners anteriores para evitar duplicação
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Adiciona novos event listeners
            newButton.addEventListener('click', (e) => this.handleReorderButtonClick(e, newButton));
            newButton.addEventListener('mousedown', (e) => this.handleControlButtonMouseDown(e, newButton));
        });

        // Inicializa animações para outros botões de controle
        const otherButtons = document.querySelectorAll('.control-btn:not(.reorder-btn)');
        otherButtons.forEach(button => {
            button.addEventListener('click', (e) => this.handleControlButtonClick(e, button));
            button.addEventListener('mousedown', (e) => this.handleControlButtonMouseDown(e, button));
        });
    }

    /**
     * Manipula clique em botões de controle
     */
    handleControlButtonClick(event, button) {
        // Previne propagação se o botão estiver desabilitado
        if (button.disabled || button.classList.contains('disabled')) {
            event.preventDefault();
            event.stopPropagation();
            return;
        }

        // Adiciona efeito ripple
        this.addRippleEffect(button, event);

        // Anima clique
        this.animateButtonClick(button);

        // Se for botão de reordenação, adiciona feedback de sucesso após um delay
        if (button.onclick && button.onclick.toString().includes('moveLink')) {
            setTimeout(() => {
                this.animateButtonSuccess(button);
            }, 100);
        }
    }

    /**
     * Manipula clique específico em botões de reordenação
     */
    handleReorderButtonClick(event, button) {
        event.preventDefault();
        event.stopPropagation();

        // Previne ação se desabilitado
        if (button.disabled || button.classList.contains('disabled')) {
            return;
        }

        const linkId = button.dataset.linkId;
        const direction = button.dataset.direction;

        if (linkId && direction) {
            // Adiciona efeito ripple
            this.addRippleEffect(button, event);

            // Anima clique
            this.animateButtonClick(button);

            // Executa movimento com animação
            this.moveLink(linkId, direction);

            // Feedback de sucesso após movimento
            setTimeout(() => {
                this.animateButtonSuccess(button);
            }, 350);
        }
    }

    /**
     * Manipula mousedown em botões de controle
     */
    handleControlButtonMouseDown(event, button) {
        // Previne ação se desabilitado
        if (button.disabled || button.classList.contains('disabled')) {
            event.preventDefault();
            return;
        }
    }
}

// Adiciona estilos de animação para o toast
const toastStyles = document.createElement('style');
toastStyles.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(toastStyles);

// Inicializa o sistema quando o DOM estiver carregado
let configManager;
document.addEventListener('DOMContentLoaded', () => {
    configManager = new ConfigManager();
});
