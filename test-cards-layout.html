<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Layout Cards Mobile</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background: var(--primary-color);
            padding: 20px;
            font-family: 'Poppins', sans-serif;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(45, 45, 45, 0.5);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: var(--text-light);
        }
        
        .test-title {
            color: var(--accent-color);
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .test-description {
            color: var(--text-gray);
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .viewport-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--accent-color);
            color: var(--primary-color);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.8rem;
            z-index: 1000;
        }
        
        .links-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .test-note {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: var(--text-light);
            font-size: 0.9rem;
        }
        
        .test-note strong {
            color: var(--accent-color);
        }
    </style>
</head>
<body>
    <div class="viewport-indicator" id="viewport-indicator">
        <span id="current-size">Carregando...</span>
    </div>

    <div class="test-container">
        <header class="test-header">
            <h1 class="test-title">🧪 Teste - Layout Cards Mobile</h1>
            <p class="test-description">
                Validação das correções implementadas nos cards de configuração (.link-item)
            </p>
        </header>

        <div class="test-note">
            <strong>📱 Layout Mobile (< 768px):</strong><br>
            • Estrutura vertical em stack<br>
            • Título + ícone no topo<br>
            • Controles organizados em grid<br>
            • URL oculta para economizar espaço<br>
            • Texto sem quebra inadequada
        </div>

        <div class="test-note">
            <strong>💻 Layout Desktop (≥ 768px):</strong><br>
            • Layout horizontal tradicional<br>
            • URL visível<br>
            • Controles em linha<br>
            • Text truncation aplicado
        </div>

        <div class="links-list">
            <!-- Card 1: WhatsApp com nome longo para testar quebra -->
            <div class="link-item">
                <div class="link-item-header">
                    <div class="link-preview" style="background-color: #25d366">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="link-info">
                        <div class="link-details">
                            <h4>WhatsApp Business Atendimento</h4>
                            <small>https://wa.me/5511999999999?text=Olá! Gostaria de agendar um horário</small>
                        </div>
                        <div class="link-controls">
                            <div class="toggle-switch active"></div>
                            <button class="control-btn edit" title="Editar link">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="control-btn" title="Mover para cima">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="control-btn" title="Mover para baixo">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <button class="control-btn danger" title="Remover link">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 2: Instagram -->
            <div class="link-item">
                <div class="link-item-header">
                    <div class="link-preview" style="background-color: #e4405f">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <div class="link-info">
                        <div class="link-details">
                            <h4>Instagram</h4>
                            <small>https://www.instagram.com/estudio730</small>
                        </div>
                        <div class="link-controls">
                            <div class="toggle-switch active"></div>
                            <button class="control-btn edit" title="Editar link">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="control-btn" title="Mover para cima">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            <button class="control-btn" title="Mover para baixo">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <button class="control-btn danger" title="Remover link">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 3: Site com nome muito longo -->
            <div class="link-item">
                <div class="link-item-header">
                    <div class="link-info">
                        <div class="link-preview" style="background-color: #6c5ce7">
                            <i class="fas fa-globe"></i>
                        </div>
                        <div class="link-details">
                            <h4>Site Oficial Estúdio730 Barbearia</h4>
                            <small>https://www.estudio730.com.br/servicos/cortes-masculinos</small>
                        </div>
                    </div>
                    <div class="link-controls">
                        <div class="toggle-switch"></div>
                        <button class="control-btn edit" title="Editar link">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="control-btn" title="Mover para cima">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button class="control-btn" title="Mover para baixo">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <button class="control-btn danger" title="Remover link">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Card 4: Localização -->
            <div class="link-item">
                <div class="link-item-header">
                    <div class="link-info">
                        <div class="link-preview" style="background-color: #4285f4">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="link-details">
                            <h4>Localização</h4>
                            <small>https://maps.google.com/place/estudio730</small>
                        </div>
                    </div>
                    <div class="link-controls">
                        <div class="toggle-switch active"></div>
                        <button class="control-btn edit" title="Editar link">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="control-btn" title="Mover para cima">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button class="control-btn" title="Mover para baixo">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-note" style="margin-top: 30px;">
            <strong>🔍 Como Testar:</strong><br>
            1. Redimensione a janela para < 768px (mobile)<br>
            2. Verifique se o layout está em stack vertical<br>
            3. Confirme que não há texto quebrado inadequadamente<br>
            4. Teste touch targets (≥ 44px)<br>
            5. Redimensione para ≥ 768px (desktop)<br>
            6. Verifique se volta ao layout horizontal
        </div>
    </div>

    <script>
        // Atualizar indicador de viewport
        function updateViewportIndicator() {
            const width = window.innerWidth;
            const indicator = document.getElementById('current-size');
            
            let layout = '';
            if (width < 768) {
                layout = 'Mobile (Stack)';
            } else if (width < 1024) {
                layout = 'Tablet (Horizontal)';
            } else {
                layout = 'Desktop (Horizontal)';
            }
            
            indicator.textContent = `${width}px - ${layout}`;
        }

        // Simular funcionalidade dos toggles
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // Simular funcionalidade dos botões
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Feedback visual
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
                
                console.log('Botão clicado:', this.title || this.className);
            });
        });

        // Inicializar e atualizar em resize
        updateViewportIndicator();
        window.addEventListener('resize', updateViewportIndicator);
    </script>
</body>
</html>
