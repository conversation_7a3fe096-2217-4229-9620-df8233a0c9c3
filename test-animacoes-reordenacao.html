<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Animações de Reordenação</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: var(--bg-secondary);
            padding: 30px;
            border-radius: 20px;
            box-shadow: var(--shadow);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 15px;
            border: 1px solid var(--border-color);
        }
        
        .test-title {
            color: var(--accent-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-description {
            color: var(--text-gray);
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .test-button {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .test-button:hover {
            background: var(--accent-hover);
            transform: translateY(-2px);
        }
        
        .test-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .test-button.secondary:hover {
            background: var(--bg-primary);
        }
        
        .animation-demo {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .demo-button {
            background: var(--secondary-color);
            color: var(--text-light);
            border: 1px solid var(--text-gray);
            width: 44px;
            height: 44px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .demo-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
            border-color: var(--accent-color);
            color: var(--accent-color);
        }
        
        .demo-button:active {
            transform: translateY(0) scale(0.95);
        }
        
        .demo-button.disabled {
            opacity: 0.4;
            cursor: not-allowed;
            pointer-events: none;
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-weight: 500;
        }
        
        .status.success {
            background: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }
        
        .status.info {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
        
        .status.warning {
            background: rgba(243, 156, 18, 0.1);
            color: #f39c12;
            border: 1px solid rgba(243, 156, 18, 0.3);
        }
        
        .demo-link-item {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #1f1f1f 100%);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 8px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .demo-link-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .demo-link-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }
        
        .demo-link-text h4 {
            margin: 0 0 4px 0;
            color: var(--text-light);
            font-size: 1rem;
        }
        
        .demo-link-text small {
            color: var(--text-gray);
            font-size: 0.85rem;
        }
        
        .demo-controls {
            display: flex;
            gap: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--accent-color); margin-bottom: 30px;">
            <i class="fas fa-magic"></i> Teste de Animações de Reordenação
        </h1>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-mouse-pointer"></i> Teste 1: Animação de Clique
            </h2>
            <p class="test-description">
                Teste os efeitos visuais de clique nos botões de reordenação: scale down/up, ripple effect e feedback de cor.
            </p>
            <div class="animation-demo">
                <button class="demo-button" onclick="testClickAnimation(this)" title="Mover para cima">
                    <i class="fas fa-chevron-up"></i>
                </button>
                <button class="demo-button" onclick="testClickAnimation(this)" title="Mover para baixo">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <button class="demo-button disabled" title="Botão desabilitado">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div id="test1-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-arrows-alt-v"></i> Teste 2: Animação de Movimento
            </h2>
            <p class="test-description">
                Visualize as transições suaves quando um item é movido de posição na lista.
            </p>
            <button class="test-button" onclick="openConfigModal()">
                <i class="fas fa-cog"></i> Abrir Configurações para Testar
            </button>
            <div id="test2-status" class="status info" style="display: block;">
                Abra as configurações e teste mover os links para ver as animações de movimento.
            </div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-exclamation-triangle"></i> Teste 3: Feedback de Ação Inválida
            </h2>
            <p class="test-description">
                Teste a animação de shake quando tentar realizar uma ação inválida (mover primeiro item para cima).
            </p>
            <button class="test-button" onclick="testShakeAnimation()">
                <i class="fas fa-hand-paper"></i> Simular Ação Inválida
            </button>
            <div id="test3-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-palette"></i> Teste 4: Transições de Estado
            </h2>
            <p class="test-description">
                Observe as transições suaves entre estados: normal, hover, active e disabled.
            </p>
            <div class="animation-demo">
                <button class="demo-button" onmouseover="showStatus('test4-status', 'Hover detectado - observe a transição suave', 'info')" title="Passe o mouse">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="demo-button" onclick="toggleDisabled(this)" title="Clique para alternar estado">
                    <i class="fas fa-toggle-on"></i>
                </button>
            </div>
            <div id="test4-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-mobile-alt"></i> Teste 5: Performance Mobile
            </h2>
            <p class="test-description">
                Teste as animações em dispositivos móveis ou redimensione a janela para verificar otimizações.
            </p>
            <button class="test-button" onclick="testMobilePerformance()">
                <i class="fas fa-tachometer-alt"></i> Simular Mobile
            </button>
            <div id="test5-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-list"></i> Demo: Lista Interativa
            </h2>
            <p class="test-description">
                Demonstração de uma lista com animações de reordenação funcionais.
            </p>
            <div id="demo-list">
                <div class="demo-link-item" data-id="1">
                    <div class="demo-link-content">
                        <div class="demo-link-icon" style="background-color: #25d366;">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="demo-link-text">
                            <h4>WhatsApp</h4>
                            <small>Conversar no WhatsApp</small>
                        </div>
                    </div>
                    <div class="demo-controls">
                        <button class="demo-button disabled" title="Já está no topo">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button class="demo-button" onclick="moveDemoItem(1, 'down')" title="Mover para baixo">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
                
                <div class="demo-link-item" data-id="2">
                    <div class="demo-link-content">
                        <div class="demo-link-icon" style="background-color: #e4405f;">
                            <i class="fab fa-instagram"></i>
                        </div>
                        <div class="demo-link-text">
                            <h4>Instagram</h4>
                            <small>Seguir no Instagram</small>
                        </div>
                    </div>
                    <div class="demo-controls">
                        <button class="demo-button" onclick="moveDemoItem(2, 'up')" title="Mover para cima">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                        <button class="demo-button disabled" title="Já está no final">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Incluir os scripts necessários -->
    <script src="config-system.js"></script>
    <script src="script.js"></script>
    
    <script>
        // Funções de teste
        function testClickAnimation(button) {
            // Simula animação de clique
            button.classList.add('clicking');
            
            // Cria efeito ripple
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(212, 175, 55, 0.6)';
            ripple.style.width = ripple.style.height = '30px';
            ripple.style.left = '7px';
            ripple.style.top = '7px';
            ripple.style.animation = 'reorderRipple 0.3s ease-out';
            
            button.appendChild(ripple);
            
            setTimeout(() => {
                button.classList.remove('clicking');
                ripple.remove();
            }, 300);
            
            showStatus('test1-status', '✨ Animação de clique executada com sucesso!', 'success');
        }
        
        function openConfigModal() {
            if (typeof configManager !== 'undefined') {
                configManager.openModal();
                showStatus('test2-status', 'Modal aberto. Teste mover os links para ver as animações!', 'info');
            } else {
                showStatus('test2-status', 'ConfigManager não encontrado. Carregue a página principal primeiro.', 'warning');
            }
        }
        
        function testShakeAnimation() {
            const button = document.querySelector('.demo-button');
            button.style.animation = 'shake 0.5s ease-in-out';
            
            setTimeout(() => {
                button.style.animation = '';
            }, 500);
            
            showStatus('test3-status', '🔄 Animação de shake executada - simula ação inválida', 'warning');
        }
        
        function toggleDisabled(button) {
            button.classList.toggle('disabled');
            const isDisabled = button.classList.contains('disabled');
            button.title = isDisabled ? 'Botão desabilitado' : 'Botão habilitado';
            
            showStatus('test4-status', 
                isDisabled ? 'Botão desabilitado - observe a transição' : 'Botão habilitado - observe a transição', 
                'info');
        }
        
        function testMobilePerformance() {
            const isMobile = window.innerWidth <= 767;
            const message = isMobile 
                ? 'Modo mobile detectado - animações otimizadas ativas' 
                : 'Modo desktop - redimensione a janela para testar mobile';
            
            showStatus('test5-status', `📱 ${message}`, 'info');
        }
        
        function moveDemoItem(id, direction) {
            const items = Array.from(document.querySelectorAll('.demo-link-item'));
            const currentItem = items.find(item => item.dataset.id === id.toString());
            const currentIndex = items.indexOf(currentItem);
            
            if (!currentItem) return;
            
            const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
            
            if (newIndex < 0 || newIndex >= items.length) {
                // Simula shake para ação inválida
                currentItem.style.animation = 'shake 0.3s ease-in-out';
                setTimeout(() => {
                    currentItem.style.animation = '';
                }, 300);
                return;
            }
            
            // Simula movimento
            currentItem.style.animation = direction === 'up' ? 'linkSlideUp 0.3s ease-out' : 'linkSlideDown 0.3s ease-out';
            
            setTimeout(() => {
                // Reordena elementos
                const parent = currentItem.parentNode;
                const targetItem = items[newIndex];
                
                if (direction === 'up') {
                    parent.insertBefore(currentItem, targetItem);
                } else {
                    parent.insertBefore(currentItem, targetItem.nextSibling);
                }
                
                currentItem.style.animation = 'linkSlideIn 0.3s ease-out';
                
                // Atualiza estados dos botões
                updateDemoButtons();
                
                setTimeout(() => {
                    currentItem.style.animation = '';
                }, 300);
            }, 300);
        }
        
        function updateDemoButtons() {
            const items = Array.from(document.querySelectorAll('.demo-link-item'));
            
            items.forEach((item, index) => {
                const upButton = item.querySelector('.demo-button:first-child');
                const downButton = item.querySelector('.demo-button:last-child');
                
                // Atualiza botão "para cima"
                if (index === 0) {
                    upButton.classList.add('disabled');
                    upButton.title = 'Já está no topo';
                    upButton.onclick = null;
                } else {
                    upButton.classList.remove('disabled');
                    upButton.title = 'Mover para cima';
                    upButton.onclick = () => moveDemoItem(item.dataset.id, 'up');
                }
                
                // Atualiza botão "para baixo"
                if (index === items.length - 1) {
                    downButton.classList.add('disabled');
                    downButton.title = 'Já está no final';
                    downButton.onclick = null;
                } else {
                    downButton.classList.remove('disabled');
                    downButton.title = 'Mover para baixo';
                    downButton.onclick = () => moveDemoItem(item.dataset.id, 'down');
                }
            });
        }
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }
        
        // Inicializa demo
        document.addEventListener('DOMContentLoaded', () => {
            updateDemoButtons();
        });
    </script>
</body>
</html>
