<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Reordenação de Links</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-secondary);
            padding: 30px;
            border-radius: 20px;
            box-shadow: var(--shadow);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 15px;
            border: 1px solid var(--border-color);
        }
        
        .test-title {
            color: var(--accent-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .test-description {
            color: var(--text-gray);
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .test-button {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: var(--accent-hover);
            transform: translateY(-2px);
        }
        
        .test-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .test-button.secondary:hover {
            background: var(--bg-primary);
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            font-weight: 500;
        }
        
        .status.success {
            background: rgba(46, 204, 113, 0.1);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }
        
        .status.error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }
        
        .status.info {
            background: rgba(52, 152, 219, 0.1);
            color: #3498db;
            border: 1px solid rgba(52, 152, 219, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--accent-color); margin-bottom: 30px;">
            <i class="fas fa-bug"></i> Teste de Reordenação de Links
        </h1>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-list-ol"></i> Teste 1: Funcionalidade Básica
            </h2>
            <p class="test-description">
                Teste se os botões de reordenação (setas para cima/baixo) estão funcionando corretamente.
            </p>
            <button class="test-button" onclick="openConfigModal()">
                <i class="fas fa-cog"></i> Abrir Configurações
            </button>
            <div id="test1-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-save"></i> Teste 2: Persistência Automática
            </h2>
            <p class="test-description">
                Verificar se as mudanças de ordem são salvas automaticamente no localStorage.
            </p>
            <button class="test-button" onclick="testPersistence()">
                <i class="fas fa-database"></i> Verificar Persistência
            </button>
            <button class="test-button secondary" onclick="clearStorage()">
                <i class="fas fa-trash"></i> Limpar Storage
            </button>
            <div id="test2-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-ban"></i> Teste 3: Validação de Limites
            </h2>
            <p class="test-description">
                Testar se os botões são desabilitados corretamente para o primeiro e último item.
            </p>
            <button class="test-button" onclick="testLimits()">
                <i class="fas fa-check-circle"></i> Verificar Limites
            </button>
            <div id="test3-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-sync"></i> Teste 4: Sincronização de Interface
            </h2>
            <p class="test-description">
                Verificar se as mudanças na ordem são refletidas tanto no modal quanto na página principal.
            </p>
            <button class="test-button" onclick="testSynchronization()">
                <i class="fas fa-eye"></i> Verificar Sincronização
            </button>
            <div id="test4-status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-plus"></i> Teste 5: Novos Links
            </h2>
            <p class="test-description">
                Testar se novos links são adicionados com a ordem correta.
            </p>
            <button class="test-button" onclick="testNewLink()">
                <i class="fas fa-plus-circle"></i> Adicionar Link de Teste
            </button>
            <div id="test5-status" class="status" style="display: none;"></div>
        </div>
    </div>

    <!-- Incluir os scripts necessários -->
    <script src="config-system.js"></script>
    <script src="script.js"></script>
    
    <script>
        // Funções de teste
        function openConfigModal() {
            if (typeof configManager !== 'undefined') {
                configManager.openModal();
                showStatus('test1-status', 'Modal de configurações aberto. Teste os botões de reordenação.', 'info');
            } else {
                showStatus('test1-status', 'Erro: ConfigManager não encontrado.', 'error');
            }
        }
        
        function testPersistence() {
            const config = localStorage.getItem('estudio730_config');
            if (config) {
                const parsed = JSON.parse(config);
                const linksCount = parsed.links ? parsed.links.length : 0;
                showStatus('test2-status', `Configuração encontrada no localStorage com ${linksCount} links.`, 'success');
            } else {
                showStatus('test2-status', 'Nenhuma configuração encontrada no localStorage.', 'info');
            }
        }
        
        function clearStorage() {
            localStorage.removeItem('estudio730_config');
            showStatus('test2-status', 'Storage limpo. Recarregue a página para restaurar padrões.', 'info');
        }
        
        function testLimits() {
            if (typeof configManager !== 'undefined') {
                const links = configManager.currentConfig.links;
                const sortedLinks = [...links].sort((a, b) => a.order - b.order);
                
                if (sortedLinks.length > 0) {
                    const firstId = sortedLinks[0].id;
                    const lastId = sortedLinks[sortedLinks.length - 1].id;
                    
                    showStatus('test3-status', 
                        `Primeiro link: ${sortedLinks[0].name} (ID: ${firstId})\n` +
                        `Último link: ${sortedLinks[sortedLinks.length - 1].name} (ID: ${lastId})\n` +
                        `Abra as configurações para verificar se os botões estão desabilitados corretamente.`, 
                        'info');
                } else {
                    showStatus('test3-status', 'Nenhum link encontrado para testar.', 'error');
                }
            } else {
                showStatus('test3-status', 'Erro: ConfigManager não encontrado.', 'error');
            }
        }
        
        function testSynchronization() {
            showStatus('test4-status', 
                'Abra as configurações, mova um link e verifique se a ordem muda na página principal também.', 
                'info');
        }
        
        function testNewLink() {
            if (typeof configManager !== 'undefined') {
                const testLink = {
                    id: 'test_' + Date.now(),
                    name: 'Link de Teste',
                    url: 'https://exemplo.com',
                    icon: 'fas fa-test-tube',
                    color: '#e74c3c',
                    visible: true,
                    removable: true,
                    order: configManager.currentConfig.links.length + 1
                };
                
                configManager.currentConfig.links.push(testLink);
                configManager.renderConfigLinks();
                configManager.renderLinks();
                
                showStatus('test5-status', 'Link de teste adicionado. Verifique se aparece no final da lista.', 'success');
            } else {
                showStatus('test5-status', 'Erro: ConfigManager não encontrado.', 'error');
            }
        }
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
